/**
 * Calculates start and end dates based on a time range
 * @param timeRange - The time range to calculate dates for ('1m', '3m', '6m', '1y', 'all')
 * @returns An object containing startDate and endDate in YYYY-MM-DD format
 */
export function calculateDateRange(timeRange: string): { startDate: string; endDate:string } {
  const now = new Date();
  // Set endDate to today in YYYY-MM-DD format.
  const endDate = now.toISOString().split('T')[0];
  let startDate: Date;

  switch (timeRange) {
    case '24h':
      // 24 hours ago
      startDate = new Date(now);
      startDate.setDate(now.getDate() - 1);
      break;
    case '7d':
      // 7 days ago
      startDate = new Date(now);
      startDate.setDate(now.getDate() - 7);
      break;
    case '1m':
      // 1 month ago
      startDate = new Date(now);
      startDate.setMonth(now.getMonth() - 1);
      break;
    case '6m':
      // 6 months ago
      startDate = new Date(now);
      startDate.setMonth(now.getMonth() - 6);
      break;
    default:
      // Default to 7 days if something unexpected is passed.
      startDate = new Date(now);
      startDate.setDate(now.getDate() - 7);
  }
  
  // Return both dates in YYYY-MM-DD format.
  return { startDate: startDate.toISOString().split('T')[0], endDate };
}
  