import Link from "next/link";
import { AssetTable } from "./AssetTable";

export async function AssetSection() {
  return (
    <section id="assetTable">
      <div className="center py-16">
        <p className="mb-4">
          Thousands of cryptocurrency assets can be tracked below. Use price,
          market capitalization, and 24-hour volume as filters. Click on the
          corresponding asset below to get the asset chart. For terminology,
          Please visit our{" "}
          <span>
            <Link href={"/learn"} className="underline">
              LEARN
            </Link>
          </span>{" "}
          section.
        </p>
        <AssetTable />
      </div>
    </section>
  );
}
