"use client";
import { Coin } from "@/lib/api.interface";
import { Pagination } from "../comman/Pagination";
import { useState } from "react";
import { usePathname, useRouter } from "next/navigation";
import { Triangle } from "lucide-react";
import { useBookmarks, useCoins, useUserLoggedIn } from "@/lib/state";
import SearchBar from "../comman/SearchBar";
import { useDebounce, useScreenSize } from "@/lib/hook";
import Loader from "../comman/Loader";
import { convertToKebabCase, formatPrice, shortenText } from "@/lib/utils";
import Button from "../comman/Button";
import Link from "next/link";
import { ConfigProvider, Table, TableProps } from "antd";

export function AssetValueChange({ value }: { value: string }) {
  const isNegative = value?.includes("-");
  const updatedValue = isNegative ? value.replace("-", "") : value;

  return (
    <span
      className={`flex items-center text-sm gap-2 ${
        isNegative ? "text-red-500" : "text-green-600"
      }`}
    >
      <Triangle
        className={`w-3 h-3 ${isNegative ? "rotate-180" : "rotate-0"}`}
        fill={isNegative ? "red" : "green"}
      />
      {updatedValue}%
    </span>
  );
}

export function Bookmark({
  coin,
  isBookmarked,
}: {
  coin: Coin;
  isBookmarked: boolean;
}) {
  const {
    createBookmark,
    removeBookmark,
    removeBookmarkIsPending,
    createBookmarkIsPending,
  } = useBookmarks({});
  const isPending = removeBookmarkIsPending || createBookmarkIsPending;
  const isLoggedIn = useUserLoggedIn();
  const router = useRouter();
  const pathname = usePathname();

  return (
    <div className="w-10">
      {isPending ? (
        <Loader />
      ) : (
        <picture>
          <img
            height={24}
            width={24}
            src={isBookmarked ? "/star-filled.svg" : "/star.svg"}
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();

              if (!isLoggedIn) {
                router.push(
                  `/login?from=${pathname === "/" ? "/#assetTable" : pathname}`
                );
                return;
              }
              if (isBookmarked) {
                removeBookmark(coin.uuid);
              } else {
                createBookmark(coin.uuid);
              }
            }}
            alt={coin.name}
          />
        </picture>
      )}
    </div>
  );
}

export function AssetTable() {
  const router = useRouter();
  const [order, setOrder] = useState("DESC");
  const [orderBy, setOrderBy] = useState("marketCap");
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(20);
  const [search, setSearch] = useState("");
  const [isStableCoinsEnabled, setIsStableCoinsEnabled] = useState(true);
  const query = useDebounce(search, 500);
  const { width } = useScreenSize();
  const isMobile = width! < 768;

  const columns: TableProps<Coin>["columns"] = [
    {
      title: "",
      key: "actions01",
      dataIndex: "isBookmarked",
      width: 45,
      render: (isBookmarked: boolean, coin: Coin) => (
        <Bookmark coin={coin} isBookmarked={isBookmarked} />
      ),
    },
    {
      title: "#",
      key: "index",
      fixed: "left",
      dataIndex: "index",
      width: 50,
      render: (text: string) => (
        <span className="text-sm text-text-primary">{text}</span>
      ),
    },
    {
      title: "Asset",
      dataIndex: "name",
      key: "name",
      fixed: "left",
      align: "left",
      width: isMobile ? 100 : 200,
      render: (text: string, coin) => (
        <Link href={`/coins/${convertToKebabCase(text)}`}>
          <div className="flex gap-2">
            <picture>
              <img height={24} width={24} src={coin.iconUrl} alt={coin.name} />
            </picture>
            <span className="hidden md:block text-base font-medium text-text-primary">
              {shortenText(text, 15)}{" "}
              <span className="text-sm text-text-primary">({coin.symbol})</span>
            </span>
          </div>
        </Link>
      ),
    },
    {
      title: "Prices",
      dataIndex: "price",
      key: "price",
      width: 120,
      align: "left",
      sorter: true,
      render: (text: string) => (
        <span className="text-sm text-text-primary">
          ${isStableCoinsEnabled? text: formatPrice(parseFloat(text))}
        </span>
      ),
    },
    ...(
      !isStableCoinsEnabled ?
      [
        {
        title: "1h",
        dataIndex: "change_1h",
        key: "change_1h",
        sorter: true,
        align: "left" as const, // <-- Add 'as const' here
        width: 100,
        render: (text: string) => <AssetValueChange value={text} />,
      },
    {
      title: "24h",
      dataIndex: "change_24h",
      key: "change_24h",
      sorter: true,
      align: "left" as const,
      width: 100,
      render: (text: string) => <AssetValueChange value={text} />,
    },
    {
      title: "7d",
      dataIndex: "change_7d",
      key: "change_7d",
      sorter: true,
      align: "left" as const,
      width: 100,
      render: (text: string) => <AssetValueChange value={text} />,
    }] :
      [
    {
      title: (
        <div className="flex flex-col items-center">
          <span>24h</span>
          <span>(Change)</span>
        </div>
      ),
      dataIndex: "change_24h",
      key: "change_24h",
      sorter: true,
      align: "left" as const,
      width: 100,
      render: (text: string) => <AssetValueChange value={text} />,
    },
    {
      title: (
        <div className="flex flex-col items-center">
          <span>Stable Coin</span>
          <span>Dominance</span>
          <span>(Volume)</span>
        </div>
      ),
      dataIndex: "volume24hPercentage",
      key: "volume24hPercentage",
      sorter: true,
      align: "center" as const,
      width: 120,
      render: (text: string) => <span>{text}%</span>,
    },
    {
      title: (
        <div className="flex flex-col items-center">
          <span>Stable Coin</span>
          <span>Dominance</span>
          <span>(Market Cap)</span>
        </div>
      ),
      dataIndex: "marketCapPercentage",
      key: "marketCapPercentage",
      sorter: true,
      align: "center" as const,
      width: 140,
      render: (text: string) => <span>{text}%</span>,
    }
      ]
    ),
    
    {
      title: "24h Volume",
      dataIndex: "24hVolume",
      key: "24hVolume",
      sorter: true,
      align: "left",
      width: 150,
      render: (text: string) => (
        <span className="text-sm text-text-primary">
          ${parseFloat(text).toLocaleString()}
        </span>
      ),
    },
    {
      title: "Market Cap",
      dataIndex: "marketCap",
      key: "marketCap",
      sorter: true,
      align: "left",
      width: 150,
      render: (text: string) => (
        <span className="text-sm text-text-primary">
          ${parseFloat(text).toLocaleString()}
        </span>
      ),
    },
    {
      title: (
        <div className="flex flex-col items-center">
          <span>{isStableCoinsEnabled ? "Digital Asset Dominance": "Dominance"}</span>
          <span>(Market Cap)</span>
        </div>
      ),
      dataIndex: "dominance",
      key: "dominance",
      width: 150,
      align: "center",
      render: (text: string) => (
        <span className="text-sm text-text-primary text-center">{text}%</span>
      ),
    },
  ];

  const coinsQuery = useCoins({
    orderBy,
    offset: page === 1 ? 0 : +page * 10,
    limit: +limit,
    order,
    searchText: query,
    isStableCoinsEnabled,
  });

  const coins = coinsQuery.data?.data.coins;

  const { userBookmarksData } = useBookmarks({});
  const bookmarkedCoins = userBookmarksData?.coins[0]?.map((coin) => coin.uuid);

  const coinList = coins?.map((coin, index) => ({
    ...coin,
    index: (+page - 1) * 20 + (index + 1),
    isBookmarked: bookmarkedCoins?.includes(coin.uuid),
    price: isStableCoinsEnabled ? String(Math.round(Number(coin.price) || 0)): coin.price
  }));
  const total = coinsQuery.data?.data.stats.total || 10;

  const handlePagination = (page: number) => {
    setPage(page);
    router.push(`/#assetTable`);
  };

  const handleSearch = (value: string) => {
    setSearch(value);
  };

  const handlePageSizeChange = (value: string) => {
    setLimit(+value);
    router.push(`/#assetTable`);
  };

  const handleDefault = () => {
    setPage(1);
    setOrder("DESC");
    setOrderBy("marketCap");
    setLimit(20);
    setSearch("");
    setIsStableCoinsEnabled(false);
    router.push(`/#assetTable`);
  };

  return (
    <div>
      <div className="mb-4 flex items-center md:flex-row flex-col gap-4">
        <div className="hidden md:flex flex-1" />
        <div className="flex gap-2 w-full justify-center md:w-auto">
          <Button variant="outline" onClick={handleDefault} className="border-2 border-bg-primary">
            Cryptocurrencies
          </Button>
          <Button
            variant={isStableCoinsEnabled ? "default" : "outline"}
            onClick={() => setIsStableCoinsEnabled((prev) => !prev)}
            className="border-2 border-bg-primary"
          >
            Stable Coins
          </Button>
        </div>
        <div className="w-full flex-1 flex justify-center md:justify-end">
        <SearchBar
          handleSearch={handleSearch}
          placeholder="Search for trending assets, popular assets..."
        />
        </div>
      </div>

      <ConfigProvider
        theme={{
          components: {
            Spin: {
              colorPrimary: "#bbd955",
            },
            Table: {
              fontFamily: "DM Sans",
              colorPrimary: "#bbd955",
            },
          },
        }}
      >
        <Table
          columns={columns}
          dataSource={coinList || []}
          pagination={false}
          onChange={(_p, _f, s) => {
            // @ts-expect-error ts(2339)
            const orderBy = s.columnKey;
            // @ts-expect-error ts(2339)
            const order = s.order === "ascend" ? "ASC" : "DESC";
            setOrder(order);
            setOrderBy(orderBy!);
          }}
          scroll={{ x: "600px" }}
          loading={coinsQuery.isLoading}
          onRow={(record) => ({
            onClick: () => {
              router.push(`/coins/${convertToKebabCase(record.name)}`);
            },
          })}
          className="crypto-table"
        />
      </ConfigProvider>

      <div className="mt-4">
        <Pagination
          totalPages={total}
          currentPage={page}
          onPageChange={handlePagination}
          pageSizeOptions={[
            { label: "20", value: "20" },
            { label: "50", value: "50" },
            { label: "100", value: "100" },
          ]}
          onPageSizeChange={handlePageSizeChange}
        />
      </div>
    </div>
  );
}
