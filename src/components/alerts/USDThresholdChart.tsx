"use client";
import React, { useState, useMemo } from "react";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { cn } from "@/lib/utils";
import {
  useUsdThresholdTransactions,
} from "@/lib/state";

import Loader from "../comman/Loader";

import { calculateDateRange } from "../mofse-advance/utils";
import { DataPoint } from "../mofse-advance/TransactionCountHistoryChart";
import TransactionCountHistoryBarGraph from "../mofse-advance/TransactionCountHistoryBarGraph";
import { getAssetName } from "./utils";
// Time range options
const timeRangeOptions = ["24h", "7d", "1m"];

const USDThresholdChart = ({
  assetType,
  onChainFilter
}: {
  assetType: string;
  onChainFilter: string;
}) => {

  const [timeRange, setTimeRange] = useState<string>("1m");
  const showDate = ["7d", "1m"].includes(timeRange);

  const { startDate, endDate } = useMemo(
    () => calculateDateRange(timeRange),
    [timeRange]
  );

  const usdThresholdTransactionQuery = useUsdThresholdTransactions(
    startDate,
    endDate,
    getAssetName(assetType),
    onChainFilter
  );

  function getTransactionData() {
      return {
        data: usdThresholdTransactionQuery.data,
        isLoading: usdThresholdTransactionQuery.isLoading,
      };
  }

  const { data, isLoading } = getTransactionData();

  const formattedData: DataPoint[] =
    data?.map((item) => ({
      date: item.date,
      count: item.count,
      timestamp: new Date(item.date).getTime(),
    })) || [];

  const chartData = formattedData.sort((a, b) => a.timestamp - b.timestamp);

  return (
    <div className="flex flex-col space-y-6">
      <div className="flex justify-end items-center">
        <div className="flex space-x-2">
          <ToggleGroup
            type="single"
            value={timeRange}
            onValueChange={(value) => value && setTimeRange(value)}
            className="flex rounded overflow-hidden  border border-border-light px-2 py-1 gap-2"
          >
            {timeRangeOptions.map((option) => (
              <ToggleGroupItem
                key={option}
                value={option}
                className={cn(
                  "outline-none border-none px-3 rounded last:rounded-tr last:rounded-br first:rounded-tl first:rounded-bl"
                )}
              >
                {option}
              </ToggleGroupItem>
            ))}
          </ToggleGroup>
        </div>
      </div>

      {/* Chart */}
      {isLoading ? (
        <Loader />
      ) : (
        <>
            <TransactionCountHistoryBarGraph
              data={chartData}
              coin={assetType}
              showDate={showDate}
              onChainFilter={onChainFilter}
            />
        </>
      )}
    </div>
  );
};

export default USDThresholdChart;
