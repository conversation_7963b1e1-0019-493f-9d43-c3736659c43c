import React from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Bread<PERSON>rumbI<PERSON>,
  Bread<PERSON>rumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import Header from "@/components/comman/Header";
import { api } from "@/lib/api";
import SearchFilter from "@/components/research/SearchFilterAndList";
import { Metadata } from "next";
import Footer from "@/components/comman/Footer";

export const metadata: Metadata = {
  title: "MOFSE - Research",
  description: "Digital Assets can feel complex, but it doesn’t have to be!",
};

const Research = async () => {
  const categories = await api.research.getAllCategories();
  const categoryList = categories.researchCategories;

  return (
    <section>
      <Header />

      <section className="center py-16">
        <Breadcrumb className="mb-2">
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/">Home</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink href="/learn">Research</BreadcrumbLink>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        <h1 className="text-3xl mb-8">
          <span className="font-semibold">Explore Topics: </span>
          Digital Assets can feel complex, but it doesn’t have to be!
        </h1>

        <SearchFilter categoryList={categoryList} />
      </section>
      <Footer />
    </section>
  );
};

export default Research;
