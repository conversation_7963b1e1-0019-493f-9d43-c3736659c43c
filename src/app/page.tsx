import Header from "@/components/comman/Header";
import FeaturedNarrativeCardList from "@/components/mofse-weekly/FeaturedNarrativeCardList";
import { AssetSection } from "@/components/home/<USER>";
import ResearchCardList from "@/components/research/ResearchCardList";
import { api } from "@/lib/api";
import Image from "next/image";
import { Suspense } from "react";

import { HeroSectionChart } from "@/components/home/<USER>";
import Footer from "@/components/comman/Footer";
import AssetMarque from "@/components/home/<USER>";
import CookieConsent from "@/components/cookie/CookieConsent";
import thePrint from "../../public/the-print.png";
import theTribune from "../../public/the-tribune.webp";
import Link from "next/link";
import Button from "@/components/comman/Button";
import MarketCapitalization from "@/components/home/<USER>";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "MOFSE - Home",
  description:
    "View top cryptocurrency & digital asset prices, market cap, and trading volume. Discover weekly industry developments and insights, onchain alerts & analytics...",
};

export default async function Home() {
  const research = await api.research.getAll("", "8", "1", "", "");
  const researchList = research.researches;
  const categories = await api.research.getAllCategories();
  const categoryList = categories.researchCategories;
  const coins = await api.coins.getAll("marketCap", 0, 10, "DESC", "");
  const allCoins = coins.data.coins;

  return (
    <section>
      <Header />
      <AssetMarque allCoins={allCoins} />
      <section>
        <div className="center pt-16 pb-4 flex gap-4 flex-col tablet:flex-row">
          <div className="w-full tablet:w-2/5 tablet:pt-16">
            <h1 className="text-3xl font-bold">
              Unlocking Digital Assets in a Decentralized World
            </h1>
            <p className="text-lg text-justify mt-8 text-[#748398]">
            Our platform serves as a trusted information companion, offering a comprehensive suite of tools and insights. We bridge established frameworks with innovative perspectives, combining on chain and off chain insights. We believe in serving others and accessibility for all.
            </p>
          </div>
          <div className="w-full tablet:w-3/5">
            <HeroSectionChart />
          </div>
        </div>
      </section>

      <section className=" bg-bg-black">
        <div className="center pt-7 py-16 text-white">
          <h2 className="text-center text-3xl font-semibold mb-4">
            Digital Assets by Market Capitalization
          </h2>
          <MarketCapitalization />
        </div>
      </section>

      <Suspense fallback={<div>Loading...</div>}>
        <AssetSection />
      </Suspense>

      <section className="bg-bg-dark-white">
        <div className="center py-16">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-3xl font-semibold">Research</h2>
            <Link href={"/research"}>
              <Button
                variant="default"
                size="sm"
                className="text-text-primary font-medium"
              >
                View All
              </Button>
            </Link>
          </div>
          <p className="text-justify">
            At MOFSE, we believe in transparency and building trust with our
            users. We bring you a curated selection of research-driven
            narratives that are making an impact. Staying ahead in this dynamic
            industry requires insight into the latest developments, trends, and
            groundbreaking technologies shaping the market. Stay ahead, stay
            informed, and be part of the future of something bigger. Explore
            now!
          </p>
          <Suspense fallback={<div>Loading...</div>}>
            <ResearchCardList
              ResearchList={researchList}
              categoryList={categoryList}
            />
          </Suspense>
        </div>
      </section>

      <section className="bg-bg-black text-white">
        <div className="center py-16">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-3xl font-semibold mb-6">MOFSE Weekly</h2>
            <Link href={"/mofse-weekly"}>
              <Button
                variant="default"
                size="sm"
                className="text-text-primary font-medium"
              >
                View All
              </Button>
            </Link>
          </div>
          <p className="text-justify">
            MOFSE Weekly delivers the latest developments in the digital assets
            space, curating global insights on regulatory changes, trading
            trends, and market shifts weekly. Stay informed with our concise and
            week&apos;s most impactful stories!
          </p>
          <Suspense fallback={<div>Loading...</div>}>
            <FeaturedNarrativeCardList />
          </Suspense>
        </div>
      </section>

      <section>
        <div className="center py-16 flex flex-col-reverse gap-4 md:flex-row">
          <div className="bg-bg-primary p-8 rounded-lg flex flex-col md:flex-row items-center gap-4 w-full md:w-[70%]">
            <div className="md:w-3/4">
              <h2 className="text-3xl font-semibold mb-3">
                Join Our Community
              </h2>
              <p className="text-lg font-medium mb-3">
                We believe in sharing and serving others. Be part of a growing
                network of like-minded individuals
              </p>
              <p className="text-base mb-3">
                Discover a space where innovation meets collaboration. By
                joining our community, youll connect with passionate
                individuals, share ideas, and stay updated with the latest
                trends, insights, and opportunities. Whether youre here to
                learn, grow, or contribute—theres a place for you.
              </p>
              <p className="text-base font-medium">
                Join us today to serve others
              </p>
            </div>

            <div className="grid grid-cols-3 gap-8 md:w-1/4">
              <div>
                <Link href="https://www.linkedin.com/company/mofse-io">
                  <Image
                    src="/linkedin.png"
                    alt="linkedin"
                    width={64}
                    height={64}
                    className="rounded-full"
                  />
                </Link>
              </div>
              <div>
                <Link href="https://www.youtube.com/@MOFSEOFFICIAL">
                  <Image
                    src="/youtube.png"
                    alt="youtube"
                    width={64}
                    height={64}
                    className="rounded-full"
                  />
                </Link>
              </div>
              <div>
                <Link href="https://x.com/mofsecrypto">
                  <Image
                    src="/twitter-icon.png"
                    alt="twitter"
                    width={64}
                    height={64}
                    className="rounded-full"
                  />
                </Link>
              </div>
              <div>
                <Link href="https://t.me/mofsecom">
                  <Image
                    src="/telegram.png"
                    alt="telegram"
                    width={64}
                    height={64}
                    className="rounded-full"
                  />
                </Link>
              </div>
            </div>
          </div>
          <div className="w-full md:w-[30%] flex flex-col items-center justify-start gap-4 mt-6 md:mt-0">
            <p className="text-2xl font-semibold">As Seen On</p>

            <div className="flex flex-col gap-5">
              <div className="flex justify-center items-center z-[1]">
                <a
                  href="https://theprint.in/ani-press-releases/mofse-com-transforming-digital-asset-learning-tracking/2565638/"
                  target="_blank"
                >
                  <Image alt="print" src={thePrint} width={70} height={70} />
                </a>
              </div>
              <div>
                <a
                  href="https://www.tribuneindia.com/news/business/mofse-com-transforming-digital-asset-learning-tracking/"
                  target="_blank"
                  className="bg-white"
                >
                  <Image
                    alt="tribune"
                    src={theTribune}
                    width={200}
                    height={150}
                  />
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>
      <Footer />
      <CookieConsent />
    </section>
  );
}
