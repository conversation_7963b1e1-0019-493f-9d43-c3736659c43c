import Footer from "@/components/comman/Footer";
import Header from "@/components/comman/Header";
import SearchFilter from "@/components/mofse-weekly/SearchFilterAndList";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Metadata } from "next";
import React from "react";

export const metadata: Metadata = {
  title: "MOFSE - Mofse Weekly",
  description:
    "At MOFSE, we believe in transparency and establishing trust with our users. We bring you a curated selection of Mofse Weekly that are making an impact in the crypto ecosystem. Whether it’s next-generation blockchain solutions, innovative DeFi platforms, or utility-driven tokens, we provide you with carefully selected Mofse Weekly that are making real impact",
};

const FeaturredNarratives = async () => {
  return (
    <section>
      <Header />

      <section className="center py-16">
        <Breadcrumb className="pb-2">
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/">Home</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink href="/mofse-weekly">Mofse Weekly</BreadcrumbLink>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        <h1 className="text-3xl font-semibold mb-2">Mofse Weekly</h1>

        <p className="text-lg text-text-secondary text-justify mb-8">
          At MOFSE, we believe in transparency and establishing trust with our
          users. We bring you a curated selection of Mofse Weekly that are
          making an impact in the crypto ecosystem. Whether it’s next-generation
          blockchain solutions, innovative DeFi platforms, or utility-driven
          tokens, we provide you with carefully selected featured narratives
          that are making real impact
        </p>

        <SearchFilter />
      </section>

      <Footer />
    </section>
  );
};

export default FeaturredNarratives;
