import React from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Bread<PERSON><PERSON>bI<PERSON>,
  Bread<PERSON>rumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import Header from "@/components/comman/Header";
import { api } from "@/lib/api";
import SearchFilter from "@/components/learn/SearchFilterAndList";
import { Metadata } from "next";
import Footer from "@/components/comman/Footer";

export const metadata: Metadata = {
  title: "MOFSE - Learn",
  description:
    "Explore Topics: Digital Assets can feel complex, but it doesn’t have to be!",
};

const Learn = async () => {
  const categories = await api.categories.getAll("");
  const categoryList = categories.learnCategories;

  return (
    <section>
      <Header />

      <section className="center py-16">
        <Breadcrumb className="mb-2">
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/">Home</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink href="/learn">Learn</BreadcrumbLink>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        <h1 className="text-3xl mb-8">
          <span className="font-semibold">Explore Topics: </span>
          Digital Assets can feel complex, but it doesn’t have to be!
        </h1>

        <SearchFilter categoryList={categoryList} />
      </section>

      <Footer />
    </section>
  );
};

export default Learn;
